// Main application controller
class App {
    constructor() {
        this.currentTab = 'material';
        this.init();
    }

    init() {
        this.setupTabNavigation();
        this.setupGlobalErrorHandling();
        this.initializeComponents();
        
        // Initialize SVG icons
        Icons.initializeIcons();
        
        // Show initial toast
        Toast.success('LED Fans loaded successfully.', 2000);
    }

    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanels = document.querySelectorAll('.tab-panel');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.dataset.tab;
                this.switchTab(targetTab);
            });
        });

        // Set initial tab
        this.switchTab(this.currentTab);
    }

    switchTab(tabName) {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanels = document.querySelectorAll('.tab-panel');
        const tabNav = document.querySelector('.tab-nav');

        // Update button states
        tabButtons.forEach(button => {
            button.classList.toggle('active', button.dataset.tab === tabName);
        });

        // Update panel visibility
        tabPanels.forEach(panel => {
            panel.classList.toggle('active', panel.id === `${tabName}-tab`);
        });

        // Update sliding indicator
        if (tabNav) {
            tabNav.setAttribute('data-active', tabName);
        }

        this.currentTab = tabName;
        
        // Trigger component-specific initialization if needed
        this.onTabSwitch(tabName);
    }

    onTabSwitch(tabName) {
        // Handle any tab-specific logic
        switch (tabName) {
            case 'material':
                // Material tab is always ready
                break;
            case 'media':
                // Media tab might need to refresh available files
                if (window.mediaComponent) {
                    // Could refresh available materials here
                }
                break;
            case 'preview':
                // Preview tab might need to refresh slots
                if (window.previewComponent) {
                    // Could refresh preview data here
                }
                break;
        }
    }

    setupGlobalErrorHandling() {
        // Handle uncaught errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            Toast.error('An unexpected error occurred. Please check the console for details.');
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            Toast.error('A network or server error occurred. Please try again.');
            event.preventDefault();
        });
    }

    initializeComponents() {
        // Components will initialize themselves when DOM is ready
        // This method can be used for any global initialization
    }

    async checkBackendConnection() {
        try {
            // Try to fetch a simple endpoint to check connectivity
            await backendApi.getMaterials('audio');
            console.log('Backend connection successful');
        } catch (error) {
            console.warn('Backend connection failed:', error);
            Toast.warning('Could not connect to server. Some features may not work properly.');
        }
    }

    // Public methods for components to communicate
    refreshAvailableContent() {
        // Refresh content in all components that need it
        if (window.mediaComponent) {
            window.mediaComponent.loadAvailableContent?.();
        }
        if (window.previewComponent) {
            window.previewComponent.loadAvailableContent?.();
        }
    }

    refreshPreviewSlots() {
        // Refresh preview slots
        if (window.previewComponent) {
            window.previewComponent.loadPreviewData?.();
        }
    }
}

// Utility functions for global use
window.appUtils = {
    // Navigate to a specific tab
    navigateToTab(tabName) {
        if (window.app) {
            window.app.switchTab(tabName);
        }
    },

    // Refresh all components
    refreshAll() {
        if (window.app) {
            window.app.refreshAvailableContent();
            window.app.refreshPreviewSlots();
        }
    },

    // Show loading overlay
    showGlobalLoading() {
        let overlay = document.getElementById('global-loading');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'global-loading';
            overlay.innerHTML = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                ">
                    <div style="
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        gap: 15px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    ">
                        <div class="spinner"></div>
                        <span>Loading...</span>
                    </div>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    },

    // Hide loading overlay
    hideGlobalLoading() {
        const overlay = document.getElementById('global-loading');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
};

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + 1, 2, 3 for tab navigation
    if ((e.ctrlKey || e.metaKey) && ['1', '2', '3'].includes(e.key)) {
        e.preventDefault();
        const tabs = ['material', 'media', 'preview'];
        const tabIndex = parseInt(e.key) - 1;
        if (tabs[tabIndex] && window.app) {
            window.app.switchTab(tabs[tabIndex]);
        }
    }

    // Escape key to close modals/drawers
    if (e.key === 'Escape') {
        // Close any open modals or drawers
        const drawer = document.getElementById('file-drawer');
        const modal = document.getElementById('preview-modal');
        
        if (drawer && drawer.classList.contains('open')) {
            if (window.mediaComponent && window.mediaComponent.drawerOpen) {
                window.mediaComponent.closeDrawer();
            } else if (window.previewComponent && window.previewComponent.drawerOpen) {
                window.previewComponent.closeDrawer();
            }
        }
        
        if (modal && modal.classList.contains('open')) {
            if (window.mediaComponent && window.mediaComponent.previewModalOpen) {
                window.mediaComponent.closePreviewModal();
            }
        }
    }
});

// Initialize app when DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// Add some helpful developer tools in development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.dev = {
        // Quick access to components
        get material() { return window.materialComponent; },
        get media() { return window.mediaComponent; },
        get preview() { return window.previewComponent; },
        get api() { return window.backendApi; },
        
        // Quick actions
        switchTab: (tab) => window.app?.switchTab(tab),
        showToast: (msg, type = 'info') => Toast.show(msg, type),
        clearStorage: () => {
            localStorage.clear();
            sessionStorage.clear();
            console.log('Storage cleared');
        },
        
        // Debug helpers
        logState: () => {
            console.log('App State:', {
                currentTab: window.app?.currentTab,
                material: window.materialComponent ? {
                    pendingImages: window.materialComponent.pendingImageVideoFiles.length,
                    pendingAudio: window.materialComponent.pendingAudioFiles.length
                } : null,
                media: window.mediaComponent ? {
                    mode: window.mediaComponent.currentMode,
                    selectedVideo: !!window.mediaComponent.selectedVideo,
                    selectedImages: window.mediaComponent.selectedImages.length,
                    selectedMusic: !!window.mediaComponent.selectedMusic
                } : null,
                preview: window.previewComponent ? {
                    mode: window.previewComponent.currentMode,
                    musicSlots: window.previewComponent.musicSlots.filter(s => !s.isEmpty).length,
                    mediaSlots: window.previewComponent.mediaSlots.filter(s => !s.isEmpty).length
                } : null
            });
        }
    };
    
    console.log('Development tools available at window.dev');
} 