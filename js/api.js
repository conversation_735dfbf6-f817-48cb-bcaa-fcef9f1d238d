// Backend API client for led-fans project
// Direct integration with Python Flask backend

class BackendApiClient {
    constructor() {
        // Dynamic configuration - use the same host as the frontend for LAN access
        const hostname = window.location.hostname;
        this.baseUrl = `http://${hostname}:3002`;
        this.apiUrl = `${this.baseUrl}/api`;
    }

    // === Material File Operations ===
    async uploadMaterial(file) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            const response = await fetch(`${this.apiUrl}/materials`, {
                method: 'POST',
                body: formData
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Upload failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            console.log('File uploaded:', result);
            // Return backend response directly
            return {
                file_name: result.path.split('/').pop(),
                file_path: result.path,
                file_size: result.size
            };
        } catch (error) {
            console.error('Upload error:', error);
            throw error;
        }
    }

    async getMaterials(fileType) {
        try {
            // Use the fileType directly - no mapping needed since it matches backend expectations
            const response = await fetch(`${this.apiUrl}/materials?file_type=${encodeURIComponent(fileType)}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Get materials failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            // The backend returns a list under "file_properties_list"
            return result.file_properties_list || [];
        } catch (error) {
            console.error('Get materials error:', error);
            // Return empty array on failure to prevent crashes
            return [];
        }
    }

    async deleteMaterial(fileName, fileType) {
        try {
            const response = await fetch(`${this.apiUrl}/materials`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    file_name: fileName,
                    file_type: fileType
                })
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Delete material failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            console.log('Material deleted:', result);
            return result;
        } catch (error) {
            console.error('Delete material error:', error);
            throw error;
        }
    }

    // === Media Composition Operations ===
    async createVideoMedia(videoFilePath, startTime, endTime, musicFilePath) {
        try {
            const payload = {
                video: {
                    file_path: videoFilePath,
                    start_time: startTime,
                    end_time: endTime
                }
            };
            // Attach music only when provided
            if (musicFilePath) {
                payload.music = { file_path: musicFilePath };
            }

            const response = await fetch(`${this.apiUrl}/medias/video`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Create video media failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            console.log('Video media created:', result);
            return result;
        } catch (error) {
            console.error('Create video media error:', error);
            throw error;
        }
    }

    async createImageMedia(images, musicFilePath) {
        try {
            const payload = { images };
            // Attach music only when provided
            if (musicFilePath) {
                payload.music = { file_path: musicFilePath };
            }

            const response = await fetch(`${this.apiUrl}/medias/images`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Create image media failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            console.log('Image media created:', result);
            return result;
        } catch (error) {
            console.error('Create image media error:', error);
            throw error;
        }
    }

    async getMedias() {
        try {
            const response = await fetch(`${this.apiUrl}/medias`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Get medias failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            // The backend returns a list under "media_properties_list"
            return result.media_properties_list || {};
        } catch (error) {
            console.error('Get medias error:', error);
            return {};
        }
    }

    async deleteMedia(videoFilePath, musicFilePath) {
        try {
            const response = await fetch(`${this.apiUrl}/medias`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    video_file_path: videoFilePath,
                    music_file_path: musicFilePath
                })
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Delete media failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            console.log('Media deleted:', result);
            return result;
        } catch (error) {
            console.error('Delete media error:', error);
            throw error;
        }
    }

    // === Preview Slot Operations ===
    async getSlots(slotType) {
        try {
            const response = await fetch(`${this.apiUrl}/slots?type=${encodeURIComponent(slotType)}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Get slots failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            return result || [];
        } catch (error) {
            console.error('Get slots error:', error);
            return [];
        }
    }

    async updateSlot(slotType, slotId, videoFilePath, musicFilePath) {
        try {
            const payload = {
                type: slotType,
                slot_id: slotId
            };
            
            // Only include video_file_path if provided and not empty
            if (videoFilePath) {
                payload.video_file_path = videoFilePath;
            }
            
            // Only include music_file_path if provided and not empty
            if (musicFilePath) {
                payload.music_file_path = musicFilePath;
            }
            
            const response = await fetch(`${this.apiUrl}/slots`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Update slot failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            console.log('Slot updated:', result);
            return result;
        } catch (error) {
            console.error('Update slot error:', error);
            throw error;
        }
    }

    async clearSlot(slotType, slotId) {
        try {
            const response = await fetch(`${this.apiUrl}/slots`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    type: slotType,
                    slot_id: slotId
                })
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Clear slot failed: ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            console.log('Slot cleared:', result);
            return result;
        } catch (error) {
            console.error('Clear slot error:', error);
            throw error;
        }
    }

    // === Utility Functions ===
    getFileUrl(filePath) {
        // Return the full URL for accessing files
        return `${this.baseUrl}/${filePath}`;
    }

    // Helper method to determine file type based on MIME type
    getFileTypeFromMime(mimeType) {
        if (!mimeType) return 'unknown';
        
        if (mimeType.startsWith('image/')) return 'image';
        if (mimeType.startsWith('video/')) return 'video';
        if (mimeType.startsWith('audio/')) return 'audio';
        
        return 'unknown';
    }

    // Helper method to check if file type is supported
    isSupportedFileType(file) {
        const supportedTypes = {
            image: ['image/jpeg', 'image/jpg', 'image/png'],
            video: ['video/mp4', 'video/quicktime'],
            audio: ['audio/mpeg', 'audio/mp3', 'audio/mp4', 'audio/x-m4a', 'audio/wav', 'audio/wave']
        };
        
        const allSupported = [...supportedTypes.image, ...supportedTypes.video, ...supportedTypes.audio];
        return allSupported.includes(file.type);
    }
}

// Create global API instance
window.backendApi = new BackendApiClient(); 