// Utility functions for the led-fans project

// === File Utilities ===
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function formatDuration(seconds) {
    if (!seconds || seconds === 0) return '0:00';
    
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function truncateMiddle(str, startLen = 20, endLen = 8) {
    if (!str || str.length <= startLen + endLen) return str;
    return `${str.slice(0, startLen)}...${str.slice(-endLen)}`;
}

function getFileIcon(fileType, isSmall = true) {
    const type = (fileType || '').toLowerCase();
    
    if (type.startsWith('image/') || type === 'image') {
        return isSmall ? Icons.SmallImageIcon() : Icons.ImageIcon();
    }
    if (type.startsWith('video/') || type === 'video') {
        return isSmall ? Icons.SmallVideoIcon() : Icons.VideoIcon();
    }
    if (type.startsWith('audio/') || type === 'audio' || type === 'music') {
        return isSmall ? Icons.SmallMusicIcon() : Icons.MusicIcon();
    }
    return isSmall ? Icons.SmallFileIcon() : Icons.FileIcon();
}

function getFileTypeFromName(fileName) {
    const ext = fileName.split('.').pop().toLowerCase();
    
    if (['jpg', 'jpeg', 'png'].includes(ext)) return 'image';
    if (['mp4', 'mov'].includes(ext)) return 'video';
    if (['mp3', 'm4a', 'wav'].includes(ext)) return 'audio';
    
    return 'unknown';
}

// === Toast Notifications ===
class Toast {
    static container = null;

    static init() {
        if (!this.container) {
            this.container = document.getElementById('toast-container');
            if (!this.container) {
                this.container = document.createElement('div');
                this.container.id = 'toast-container';
                this.container.className = 'toast-container';
                document.body.appendChild(this.container);
            }
        }
    }

    static show(message, type = 'info', duration = 3000) {
        this.init();
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        toast.innerHTML = `
            <div class="toast-icon"></div>
            <div class="toast-message">${message}</div>
        `;
        
        this.container.appendChild(toast);
        
        // Auto remove after duration
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideOut 0.3s ease forwards';
                setTimeout(() => {
                    if (toast.parentElement) {
                        this.container.removeChild(toast);
                    }
                }, 300);
            }
        }, duration);
    }

    static success(message, duration) {
        this.show(message, 'success', duration);
    }

    static error(message, duration) {
        this.show(message, 'error', duration);
    }

    static warning(message, duration) {
        this.show(message, 'warning', duration);
    }

    static info(message, duration) {
        this.show(message, 'info', duration);
    }
}

// Add slideOut animation to CSS if not already present
if (!document.querySelector('style[data-toast-styles]')) {
    const style = document.createElement('style');
    style.setAttribute('data-toast-styles', 'true');
    style.textContent = `
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// === Modal/Drawer Utilities ===
class Modal {
    static show(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('open');
            document.body.style.overflow = 'hidden';
        }
    }

    static hide(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            // Stop all audio players before closing modal
            stopAllAudioPlayers();
            modal.classList.remove('open');
            document.body.style.overflow = '';
        }
    }
}

class Drawer {
    static show(drawerId) {
        const drawerBackdrop = document.getElementById(drawerId + '-backdrop');
        if (drawerBackdrop) {
            drawerBackdrop.classList.add('open');
            document.body.style.overflow = 'hidden';
            
            // Add click listener to backdrop for closing drawer
            this.setupBackdropListener(drawerId);
        }
    }

    static hide(drawerId) {
        const drawerBackdrop = document.getElementById(drawerId + '-backdrop');
        if (drawerBackdrop) {
            // Stop all audio players before closing drawer
            stopAllAudioPlayers();
            drawerBackdrop.classList.remove('open');
            document.body.style.overflow = '';
            
            // Remove click listener
            this.removeBackdropListener(drawerId);
        }
    }

    static setupBackdropListener(drawerId) {
        const drawerBackdrop = document.getElementById(drawerId + '-backdrop');
        const drawer = document.getElementById(drawerId);
        
        if (drawerBackdrop && drawer) {
            // Remove existing listener if any
            this.removeBackdropListener(drawerId);
            
            // Add new listener
            const backdropClickHandler = (e) => {
                // Only close if clicking the backdrop, not the drawer content
                if (e.target === drawerBackdrop) {
                    this.hide(drawerId);
                }
            };
            
            drawerBackdrop.addEventListener('click', backdropClickHandler);
            
            // Store handler for later removal
            drawerBackdrop._backdropClickHandler = backdropClickHandler;
        }
    }

    static removeBackdropListener(drawerId) {
        const drawerBackdrop = document.getElementById(drawerId + '-backdrop');
        if (drawerBackdrop && drawerBackdrop._backdropClickHandler) {
            drawerBackdrop.removeEventListener('click', drawerBackdrop._backdropClickHandler);
            delete drawerBackdrop._backdropClickHandler;
        }
    }
}

// === Confirmation Dialog ===
function confirm(title, message, onConfirm, onCancel = null) {
    const overlay = document.createElement('div');
    overlay.className = 'modal open';
    overlay.innerHTML = `
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3>${title}</h3>
            </div>
            <div class="modal-body">
                <p>${message}</p>
                <div class="action-buttons" style="margin-top: 20px; justify-content: center;">
                    <button class="btn btn-secondary" id="cancel-btn">Cancel</button>
                    <button class="btn btn-danger" id="confirm-btn">Confirm</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(overlay);
    document.body.style.overflow = 'hidden';

    const confirmBtn = overlay.querySelector('#confirm-btn');
    const cancelBtn = overlay.querySelector('#cancel-btn');

    const cleanup = () => {
        document.body.removeChild(overlay);
        document.body.style.overflow = '';
    };

    confirmBtn.onclick = () => {
        cleanup();
        if (onConfirm) onConfirm();
    };

    cancelBtn.onclick = () => {
        cleanup();
        if (onCancel) onCancel();
    };

    // Close on overlay click
    overlay.onclick = (e) => {
        if (e.target === overlay) {
            cleanup();
            if (onCancel) onCancel();
        }
    };
}

// === Loading States ===
function setLoading(element, isLoading) {
    if (isLoading) {
        element.classList.add('loading');
        if (element.tagName === 'BUTTON') {
            element.disabled = true;
            const originalText = element.textContent;
            element.dataset.originalText = originalText;
            element.innerHTML = '<span class="spinner"></span> Loading...';
        }
    } else {
        element.classList.remove('loading');
        if (element.tagName === 'BUTTON') {
            element.disabled = false;
            if (element.dataset.originalText) {
                element.textContent = element.dataset.originalText;
                delete element.dataset.originalText;
            }
        }
    }
}

// === Time Utilities ===
function parseTimeString(timeStr) {
    // Parse "ss.sss" format (seconds.milliseconds)
    const numValue = parseFloat(timeStr);
    return isNaN(numValue) ? 0 : numValue;
}

function formatTimeString(totalSeconds) {
    // Format as "ss.sss" (seconds.milliseconds)
    return totalSeconds.toFixed(3);
}

// === Preview URL Utilities ===
function getPreviewUrl(file) {
    if (!file) return null;
    // For image files, use the file itself
    if (file.file_path && (
        file.file_path.includes('images') || 
        file.type === 'image' ||
        file.file_path.match(/\.(jpg|jpeg|png|gif|webp)$/i)
    )) {
        return backendApi.getFileUrl(file.file_path);
    }
    
    // Check for alternative path properties (for different data structures)
    if (file.media_path && file.media_path.includes('images')) {
        return backendApi.getFileUrl(file.media_path);
    }
    
    // For video files, return file path; HTML5 video will handle poster/first frame
    if (file.file_path && file.file_path.match(/\.(mp4|mov|webm|m4v)$/i)) {
        return backendApi.getFileUrl(file.file_path);
    }
    if (file.media_path && file.media_path.match(/\.(mp4|mov|webm|m4v)$/i)) {
        return backendApi.getFileUrl(file.media_path);
    }
    return null;
}

// === Placeholder / Fallback Utilities ===
function createPlaceholderElement(className = 'file-preview-image') {
    return `<div class="placeholder-preview ${className}">${Icons.PlaceholderIcon()}</div>`;
}

function handleMediaError(element, className = 'file-preview-image') {
    try {
        const placeholder = createPlaceholderElement(className);
        // Replace the failed element with a placeholder to avoid broken UI
        element.insertAdjacentHTML('afterend', placeholder);
        element.remove();
    } catch (_) {}
}

function createPreviewElement(file, altText = 'Preview', className = 'file-preview-image') {
    const previewUrl = getPreviewUrl(file);
    if (previewUrl) {
        const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(previewUrl);
        if (isImage) {
            return `<img src="${previewUrl}" alt="${altText}" class="${className}" onerror="window.utils.handleMediaError(this, '${className}')" />`;
        }
        // For video
        const videoClass = className.replace('image', 'video');
        return createVideoPlayer(previewUrl, videoClass);
    }
    return createPlaceholderElement(className);
}



// === Drag and Drop Utilities ===
function setupDragAndDrop(element, onDrop, acceptedTypes = []) {
    element.addEventListener('dragover', (e) => {
        e.preventDefault();
        element.classList.add('dragover');
    });

    element.addEventListener('dragleave', (e) => {
        e.preventDefault();
        element.classList.remove('dragover');
    });

    element.addEventListener('drop', (e) => {
        e.preventDefault();
        element.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files);
        const validFiles = acceptedTypes.length > 0 
            ? files.filter(file => acceptedTypes.includes(file.type))
            : files;
        
        if (validFiles.length > 0) {
            onDrop(validFiles);
        }
    });
}

// === Audio Control Utilities ===
function stopAllAudioPlayers() {
    // Stop all audio elements in custom audio players
    const audioElements = document.querySelectorAll('.custom-audio-player audio');
    audioElements.forEach(audio => {
        if (!audio.paused) {
            audio.pause();
            audio.currentTime = 0;
        }
    });
    
    // Also stop any media-controller elements
    const mediaControllers = document.querySelectorAll('.custom-audio-player media-controller');
    mediaControllers.forEach(controller => {
        const mediaElement = controller.querySelector('audio');
        if (mediaElement && !mediaElement.paused) {
            mediaElement.pause();
            mediaElement.currentTime = 0;
        }
    });
}

function stopOtherAudioPlayers(currentAudio) {
    // Stop all audio elements except the current one
    const audioElements = document.querySelectorAll('.custom-audio-player audio');
    audioElements.forEach(audio => {
        if (audio !== currentAudio && !audio.paused) {
            audio.pause();
            audio.currentTime = 0;
        }
    });
    
    // Also stop any media-controller elements except the current one
    const mediaControllers = document.querySelectorAll('.custom-audio-player media-controller');
    mediaControllers.forEach(controller => {
        const mediaElement = controller.querySelector('audio');
        if (mediaElement !== currentAudio && mediaElement && !mediaElement.paused) {
            mediaElement.pause();
            mediaElement.currentTime = 0;
        }
    });
}

// === Custom Audio Player ===
function createCustomAudioPlayer(audioUrl) {
    const audioId = 'audio_' + Math.random().toString(36).substr(2, 9);
    return `
        <media-controller
            audio
            class="custom-audio-player"
            onclick="event.stopPropagation();"
        >
            <audio
                id="${audioId}"
                slot="media"
                src="${audioUrl}"
                crossorigin
                onplay="window.utils.stopOtherAudioPlayers(this)"
            ></audio>

            <!-- Mobile Progress Bar (Top Row) -->
            <media-time-range class="mobile-progress-bar" onclick="event.stopPropagation();">
                <media-preview-time-display slot="preview"></media-preview-time-display>
            </media-time-range>

            <media-control-bar>
                <!-- Control Panel Group -->
                <div class="control-panel">
                    <media-seek-backward-button seekoffset="10" onclick="event.stopPropagation();">
                        <div slot="icon">${Icons.BackwardIcon('#64748b')}</div>
                    </media-seek-backward-button>
                    <media-play-button onclick="event.stopPropagation();">
                        <div slot="play">${Icons.PlayIcon()}</div>
                        <div slot="pause">${Icons.PauseIcon()}</div>
                    </media-play-button>
                    <media-seek-forward-button seekoffset="10" onclick="event.stopPropagation();">
                        <div slot="icon">${Icons.ForwardIcon('#64748b')}</div>
                    </media-seek-forward-button>
                </div>

                <!-- Separator -->
                <div class="desktop-separator"></div>

                <!-- Time Display -->
                <media-time-display class="desktop-time"></media-time-display>

                <!-- Desktop Progress Bar -->
                <media-time-range class="desktop-progress-bar" onclick="event.stopPropagation();">
                    <media-preview-time-display slot="preview"></media-preview-time-display>
                </media-time-range>

                <!-- Duration Display -->
                <media-duration-display class="desktop-duration"></media-duration-display>

                <!-- Time Info (Mobile Combined Display) -->
                <div class="mobile-time-info">
                    <media-time-display style="--media-text-color: #4b5563;"></media-time-display>
                    <span class="time-separator">/</span>
                    <media-duration-display style="--media-text-color: #4b5563;"></media-duration-display>
                </div>

                <!-- Volume Control -->
                <media-mute-button onclick="event.stopPropagation();">
                    <div slot="high">${Icons.VolumeHighIcon('#64748b')}</div>
                    <div slot="off">${Icons.VolumeOffIcon('#64748b')}</div>
                </media-mute-button>
            </media-control-bar>
        </media-controller>
    `;
}



// === Custom Video Player ===
function createVideoPlayer(videoUrl, className = 'slot-video-player') {
    if (!videoUrl) {
        return createPlaceholderElement(className);
    }
    
    return `
        <video 
            class="${className}"
            src="${videoUrl}" 
            controls 
            onclick="event.stopPropagation();"
            onerror="window.utils.handleMediaError(this, '${className}')"
            style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;"
        >
            Your browser does not support the video tag.
        </video>
    `;
}

// === Global Instances ===
window.Toast = Toast;
window.Modal = Modal;
window.Drawer = Drawer;

// Export utilities to global scope
window.utils = {
    formatFileSize,
    formatDuration,
    truncateMiddle,
    getFileIcon,
    getFileTypeFromName,
    setLoading,
    parseTimeString,
    formatTimeString,
    getPreviewUrl,
    createPlaceholderElement,
    handleMediaError,
    createPreviewElement,
    createVideoPlayer,
    setupDragAndDrop,
    confirm,
    createCustomAudioPlayer,
    stopAllAudioPlayers,
    stopOtherAudioPlayers
}; 