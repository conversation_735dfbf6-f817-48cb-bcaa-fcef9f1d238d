/* Preview Component Styles */

/* Preview Container */
.preview-container {
    min-height: 600px;
}

/* Slots Grid */
.slots-grid {
    display: grid;
    gap: 16px;
    margin-top: 24px;
}

/* Grid Layout Based on Mode */
.slots-grid.music-mode {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.slots-grid.media-mode {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Slot Card - reusing file-card styles */
.slot-card {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
}

.slot-card.empty {
    border: 2px dashed #d9d9d9;
    background: none;
    padding: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.slot-card.empty:hover {
    border-color: #1890ff;
    background-color: #f0f9ff;
}

.slot-preview-placeholder {
    font-size: 24px;
    color: #999;
}

/* Override icon color for empty slots to match gray theme */
.slot-card.empty .placeholder-icon svg {
    color: #9ca3af;
}

.slot-card.empty .placeholder-text {
    color: #666;
    font-size: 16px;
}

/* Music Slot Specific */
.music-slot .file-card-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 32px;
}

/* File Card Header - General styling for all slot types */
.file-card-header {
    padding: 8px 12px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-card-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.media-slot .file-card-preview {
    height: 180px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* Let media slot act as a vertical flex container so no-audio section can stretch */
.media-slot {
    display: flex;
    flex-direction: column;
}

.media-slot .file-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Video Player in Media Slot */
.media-slot .file-preview-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-slot .placeholder-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #999;
    font-size: 24px;
}

.media-slot .file-card-meta {
    padding: 8px 16px;
    display: flex;
    align-items: center;
}

.media-slot .file-card-meta .file-name {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Media Card Audio Player */
.media-card-audio {
    padding: 12px;
    background: white;
    border-top: 1px solid rgba(71, 85, 105, 0.1);
}

.media-card-audio .custom-audio-player {
    background: transparent;
    border: none;
    padding: 12px 12px 0;
    border-radius: 0;
}

.media-card-audio .custom-audio-player media-control-bar {
    height: 48px;
    padding: 0;
    background: transparent;
    box-shadow: none;
    border: none;
    border-radius: 0;
}

.media-card-audio .custom-audio-player .control-panel {
    flex: 0 0 140px;
    max-width: 140px;
}

.media-card-audio .custom-audio-player .mobile-progress-bar {
    display: block;
}

.media-card-audio .custom-audio-player .mobile-time-info {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
}

.media-card-audio .custom-audio-player .mobile-time-info .time-separator {
    margin: 0 6px;
}

.media-card-audio .custom-audio-player .desktop-progress-bar,
.media-card-audio .custom-audio-player .desktop-separator,
.media-card-audio .custom-audio-player .desktop-time,
.media-card-audio .custom-audio-player .desktop-duration {
    display: none;
}

/* Media Card No Audio Placeholder */
.media-card-no-audio {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    background: white;
    border-top: 1px solid rgba(71, 85, 105, 0.1);
    /* Auto-fit height: default minimal height; can stretch with card */
    min-height: 0;
}

.media-card-no-audio .no-audio-label {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

/* Allow no-audio section to expand when card is taller (aligning with cards that have audio) */
.media-slot .media-card-no-audio {
    flex: 1 1 auto;
}

/* Drawer (preview-responsive) media cards should also allow no-audio to expand when needed */
.preview-responsive .file-card {
    display: flex;
    flex-direction: column;
}

.preview-responsive .media-card-no-audio {
    flex: 1 1 auto;
}



/* Audio Content in Music Slots */
.slot-audio-content {
    padding: 12px 12px 0;
    background: white;
    border-bottom: 1px solid rgba(71, 85, 105, 0.1);
}

.slot-audio-content .custom-audio-player {
    background: transparent;
    border: none;
    padding: 0;
    border-radius: 0;
    padding: 12px;
}

.slot-audio-content .custom-audio-player media-control-bar {
    height: 48px;
    padding: 0;
    background: transparent;
    box-shadow: none;
    border: none;
    border-radius: 0;
}

.slot-audio-content .custom-audio-player .control-panel {
    flex: 0 0 140px;
    max-width: 140px;
}

.slot-audio-content .custom-audio-player .mobile-progress-bar {
    display: block;
}

.slot-audio-content .custom-audio-player .mobile-time-info {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
}

.slot-audio-content .custom-audio-player .mobile-time-info .time-separator {
    margin: 0 6px;
}

.slot-audio-content .custom-audio-player .desktop-progress-bar,
.slot-audio-content .custom-audio-player .desktop-separator,
.slot-audio-content .custom-audio-player .desktop-time,
.slot-audio-content .custom-audio-player .desktop-duration {
    display: none;
}

/* Loading State for Slots */
.slot-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.slot-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Slot Animation */
.slot-card.updating {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Error State */
.slot-card.error {
    border-color: #ff4d4f;
    background-color: #fff2f0;
}

.slot-card.error .slot-title {
    color: #cf1322;
}

.error-message {
    padding: 8px 16px;
    color: #cf1322;
    font-size: 12px;
    background-color: #fff2f0;
    border-top: 1px solid #ffccc7;
}

/* Responsive Design - Preview Tab uses 640px breakpoint for all content */
@media (max-width: 640px) {
    /* All Preview tab slots and drawers use 640px breakpoint */
    .slots-grid.music-mode,
    .slots-grid.media-mode {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .slots-grid {
        margin-top: 16px;
    }
}

/* Accessibility */
.slot-card:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
}

/* Slot Header Actions */
.slot-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Slot Play Button */
.slot-play-btn {
    color: #1890ff;
    border: none;
    background: transparent;
    cursor: pointer;
    height: 24px;
    padding: 0 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
    font-size: 12px;
    font-weight: 500;
}

.slot-play-btn svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

.slot-play-btn .button-label {
    color: #1890ff;
    font-size: 13px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Slot Close Button */
.slot-close-btn {
    color: #9ca3af;
    border: none;
    background: transparent;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.slot-close-btn svg {
    width: 12px;
    height: 12px;
}

.slot-close-btn:hover {
    background-color: #f3f4f6;
    color: #6b7280;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .slot-card {
        border-width: 2px;
    }
} 