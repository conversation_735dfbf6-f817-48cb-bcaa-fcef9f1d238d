/* Media Component Styles */

/* Media Container */
.media-container {
    min-height: 500px;
}

/* Selected File List */
.selected-file-list {
    margin-top: 16px;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.selected-file-list:empty::before {
    content: 'No files selected';
    color: #999;
    font-size: 14px;
    text-align: center;
    padding: 20px;
    display: block;
}

/* File Card in Selected List */
.selected-file-card {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.2s ease;
}

.selected-file-card:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

/* Image Timeline (for image mode) */
.image-timeline {
    margin-top: 16px;
    background-color: #fafafa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e8e8e8;
}

/* Video Timeline (for video mode) */
.video-timeline {
    margin-top: 16px;
    background-color: #fafafa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e8e8e8;
}

.timeline-header {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
}

.timeline-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.timeline-subtitle {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}



/* Timeline List Container */
.timeline-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Timeline Item - New Left-Time Layout */
.timeline-item {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 0;
    transition: all 0.2s ease;
}

.timeline-item:hover {
    background-color: #fafafa;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

/* Left Section: File Information */
.timeline-file-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;
    padding-right: 12px;
}

/* Divider */
.timeline-divider {
    width: 1px;
    height: 40px;
    background-color: #e8e8e8;
    flex-shrink: 0;
    margin: 0 12px;
}

/* Right Section: Control Pane */
.timeline-content-section {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-shrink: 0;
}

/* Time Section within Control Pane */
.timeline-time-section {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.time-group {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.time-label {
    font-size: 15px;
    color: #666;
    margin: 0;
    white-space: nowrap;
    font-weight: 500;
    min-width: 72px;
}

.time-input {
    width: 88px;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    background-color: white;
    transition: border-color 0.2s ease;
    font-family: monospace;
    font-weight: 500;
}

.time-input:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* Video Time Section - Two Column Layout */
.video-time-section {
    flex: 1;
    max-width: none;
}

.video-time-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    width: 100%;
}

.video-time-group .time-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.video-time-group .time-label {
    font-size: 14px;
    color: #666;
    margin: 0;
    white-space: nowrap;
    font-weight: 500;
    min-width: auto;
    text-align: center;
}

.timeline-thumbnail {
    width: 52px;
    height: 52px;
    flex-shrink: 0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    position: relative;
}

.timeline-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.timeline-index {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2));
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 6px 8px 4px;
    line-height: 1;
    text-align: center;
    border-radius: 0 0 7px 7px;
}

.timeline-file-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.timeline-file-name {
    font-size: 15px;
    font-weight: 600;
    color: #262626;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    margin: 0;
    letter-spacing: -0.01em;
}

.timeline-file-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    font-weight: 500;
    color: #999;
    line-height: 1.3;
    margin-top: 2px;
}



.file-size {
    color: #999;
    font-weight: 500;
}

.separator {
    color: inherit;
}

/* Controls within Content Section */
.timeline-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    justify-content: flex-end;
}

.timeline-btn {
    padding: 8px;
    min-width: 36px;
    height: 36px;
    font-size: 12px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: 1px solid #d9d9d9;
    background-color: white;
    color: #595959;
}

.timeline-btn svg {
    width: 16px;
    height: 16px;
}

.timeline-btn:hover:not(:disabled) {
    background-color: #fafafa;
    border-color: #40a9ff;
    color: #1890ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}



.timeline-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: #f5f5f5;
    border-color: #e6e6e6;
    color: #bfbfbf;
}

/* Music Section */
.music-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e8e8e8;
}

/* Drawer Content - Base Styles */
.drawer-file-grid {
    display: grid;
    gap: 16px;
    margin-top: 16px;
}

.drawer-file-grid.single-col {
    grid-template-columns: 1fr;
}

.drawer-file-grid.two-col {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* Audio-related Drawer (640px responsive) */
.drawer-file-grid.audio-responsive {
    grid-template-columns: 1fr;
}

/* Image/Video Drawer (always two-col, no responsive) */
.drawer-file-grid.media-fixed {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* Preview Content Drawer (640px responsive) */
.drawer-file-grid.preview-responsive {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* File Card in Drawer */
.file-card {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
}

.file-card-header {
    padding: 8px 12px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-card-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-card-delete-btn {
    color: #ff4d4f;
    border: none;
    background: transparent;
    padding: 4px;
    border-radius: 4px;
    opacity: 1;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-card-delete-btn svg {
    width: 16px;
    height: 16px;
}

.file-card-delete-btn:hover {
    background-color: #fff2f0;
}

.file-card-preview {
    height: 180px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.file-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Video player used in cards */
.file-preview-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-card-meta {
    padding: 8px 16px;
    display: flex;
    align-items: center;
}

.file-card-meta .file-name {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Music Card */
.music-card {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
}

.music-card-header {
    padding: 8px 12px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.music-card-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}



/* Preview Modal Content */
.preview-content {
    text-align: center;
}

.preview-content img,
.preview-content video {
    max-width: 100%;
    max-height: 70vh;
    border-radius: 8px;
}

.preview-info {
    margin-top: 16px;
    text-align: left;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
}

.preview-info p {
    margin: 4px 0;
    font-size: 14px;
}



/* Responsive Design */
@media (max-width: 768px) {
    .drawer-file-grid {
        gap: 12px;
    }
    
    .drawer-file-grid.two-col {
        gap: 12px;
    }
    
    .file-card-header {
        padding: 6px 10px;
    }
}

/* Responsive Design for Different Content Types */
@media (max-width: 640px) {
    /* Audio-related content (Preview tab + Media tab audio) */
    .drawer-file-grid.audio-responsive {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    /* Preview content drawer (Preview tab media compositions) */
    .drawer-file-grid.preview-responsive {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    /* Media tab image/video drawers remain two-col (no responsive) */
    .drawer-file-grid.media-fixed {
        grid-template-columns: 1fr 1fr;
        gap: 12px; /* Only adjust gap, not columns */
    }
}

@media (max-width: 576px) {
    /* Mobile Timeline Item - Stack Layout */
    .timeline-item {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
        padding: 20px;
        border-radius: 12px;
    }
    
    /* Mobile File Section - Avatar layout (left image, right text) */
    .timeline-file-section {
        flex: none;
        width: auto;
        min-width: 0;
        flex-direction: row;
        align-items: center;
        gap: 16px;
        text-align: left;
        padding-right: 0;
    }
    
    /* Hide divider on mobile */
    .timeline-divider {
        display: none;
    }
    
    /* Mobile Content Section - Stack layout */
    .timeline-content-section {
        flex-direction: column;
        gap: 16px;
    }
    
    /* Mobile Time Section - Full width card */
    .timeline-time-section {
        width: 100%;
        flex: none;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        padding: 12px 16px;
    }
    
    /* Mobile Video Time Section - Keep two column layout but adjust sizing */
    .video-time-section {
        padding: 16px;
    }
    
    .video-time-group {
        grid-template-columns: 1fr 1fr;
        gap: 12px;
    }
    
    .video-time-group .time-label {
        font-size: 15px;
        font-weight: 500;
        color: #495057;
    }
    
    .video-time-group .time-input {
        width: 100%;
        padding: 12px 14px;
        font-size: 15px;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        font-weight: 500;
    }
    
    .video-time-group .time-input:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
    }
    
    .time-group {
        justify-content: center;
        gap: 12px;
    }
    
    .time-label {
        font-size: 16px;
        font-weight: 500;
        color: #495057;
        min-width: auto;
    }
    
    .time-input {
        width: 100px;
        padding: 12px 14px;
        font-size: 16px;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        font-weight: 500;
    }
    
    .time-input:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
    }
    
    .timeline-thumbnail {
        width: 80px;
        height: 80px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .timeline-index {
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2));
        font-size: 12px;
        padding: 8px 10px 6px;
        border-radius: 0 0 11px 11px;
    }
    
    .timeline-file-info {
        display: flex;
        flex-direction: column;
        gap: 6px;
        flex: 1;
        min-width: 0;
    }
    
    .timeline-file-name {
        font-size: 16px;
        font-weight: 600;
        text-align: left;
    }
    
    .timeline-file-meta {
        justify-content: flex-start;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #666;
    }
    

    
    /* Mobile Controls - Grid layout */
    .timeline-controls {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 12px;
        width: 100%;
        justify-content: stretch;
    }
    
    /* Ensure all buttons stretch to fill grid cells */
    .timeline-controls .btn {
        width: 100%;
        justify-self: stretch;
        height: 100%;
    }
    
    /* Video mode timeline controls - remove button on right side */
    .video-timeline .timeline-controls {
        grid-template-columns: none;
    }
}

/* Placeholder Preview Styles */
.placeholder-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    color: #bfbfbf;
}